AWSTemplateFormatVersion: "2010-09-09"
Description: "GameFlex AWS Infrastructure - LocalStack Development Environment"

Parameters:
  Environment:
    Type: String
    Default: development
    AllowedValues:
      - development
      - staging
      - production
    Description: Environment name

  ProjectName:
    Type: String
    Default: gameflex
    Description: Project name for resource naming

  DomainName:
    Type: String
    Default: localhost
    Description: Domain name for the application

  ApiDomainName:
    Type: String
    Default: localhost
    Description: API domain name

  CertificateArn:
    Type: String
    Default: ""
    Description: SSL certificate ARN (optional for development)

  EnableCloudFront:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable CloudFront distribution

  EnableWAF:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable WAF protection

  EnableXRay:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable X-Ray tracing

  LambdaMemorySize:
    Type: Number
    Default: 256
    Description: Lambda function memory size

  LambdaTimeout:
    Type: Number
    Default: 30
    Description: Lambda function timeout

  S3BucketVersioning:
    Type: String
    Default: Suspended
    AllowedValues: [Enabled, Suspended]
    Description: S3 bucket versioning

  S3BucketEncryption:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable S3 bucket encryption

  EnableS3AccessLogging:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable S3 access logging

  CognitoPasswordMinLength:
    Type: Number
    Default: 8
    Description: Minimum password length

  CognitoPasswordRequireUppercase:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require uppercase in password

  CognitoPasswordRequireLowercase:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require lowercase in password

  CognitoPasswordRequireNumbers:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: Require numbers in password

  CognitoPasswordRequireSymbols:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Require symbols in password

  CognitoMfaConfiguration:
    Type: String
    Default: "OFF"
    AllowedValues: ["OFF", "ON", "OPTIONAL"]
    Description: MFA configuration

  ApiGatewayThrottleBurstLimit:
    Type: Number
    Default: 200
    Description: API Gateway throttle burst limit

  ApiGatewayThrottleRateLimit:
    Type: Number
    Default: 100
    Description: API Gateway throttle rate limit

  EnableApiGatewayLogging:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable API Gateway logging

  ApiGatewayLogLevel:
    Type: String
    Default: INFO
    AllowedValues: [ERROR, INFO]
    Description: API Gateway log level

  EnableDetailedMetrics:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable detailed metrics

  AlertingEmail:
    Type: String
    Default: <EMAIL>
    Description: Email for alerts

  EnableAlerts:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable alerting

  LogRetentionDays:
    Type: Number
    Default: 7
    Description: Log retention in days

  EnableVPCEndpoints:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Enable VPC endpoints

  VPCCidr:
    Type: String
    Default: 10.0.0.0/16
    Description: VPC CIDR block

  PublicSubnet1Cidr:
    Type: String
    Default: ********/24
    Description: Public subnet 1 CIDR

  PublicSubnet2Cidr:
    Type: String
    Default: ********/24
    Description: Public subnet 2 CIDR

  PrivateSubnet1Cidr:
    Type: String
    Default: ********/24
    Description: Private subnet 1 CIDR

  PrivateSubnet2Cidr:
    Type: String
    Default: ********/24
    Description: Private subnet 2 CIDR

Conditions:
  IsDevelopment: !Equals [!Ref Environment, development]
  IsProduction: !Equals [!Ref Environment, production]

Resources:
  # S3 Buckets
  MediaBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ProjectName}-media-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3000

  AvatarsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ProjectName}-avatars-${Environment}"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3000

  TempBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ProjectName}-temp-${Environment}"
      LifecycleConfiguration:
        Rules:
          - Id: DeleteTempFiles
            Status: Enabled
            ExpirationInDays: 1

  # DynamoDB Tables
  UsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Users"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  UserProfilesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-UserProfiles"
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  ChannelsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Channels"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  ChannelMembersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-ChannelMembers"
      AttributeDefinitions:
        - AttributeName: channel_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: channel_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  MediaTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Media"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  PostsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Posts"
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  CommentsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Comments"
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  LikesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Likes"
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  FollowsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Follows"
      AttributeDefinitions:
        - AttributeName: follower_id
          AttributeType: S
        - AttributeName: following_id
          AttributeType: S
      KeySchema:
        - AttributeName: follower_id
          KeyType: HASH
        - AttributeName: following_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  NotificationsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub "${ProjectName}-${Environment}-Notifications"
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
        - AttributeName: created_at
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
        - AttributeName: created_at
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  # Cognito User Pool
  UserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: !Sub "${ProjectName}-users-${Environment}"
      AutoVerifiedAttributes:
        - email
      UsernameAttributes:
        - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false
      Schema:
        - Name: email
          AttributeDataType: String
          Required: true
          Mutable: true
        - Name: given_name
          AttributeDataType: String
          Required: false
          Mutable: true
        - Name: family_name
          AttributeDataType: String
          Required: false
          Mutable: true
        - Name: username
          AttributeDataType: String
          Required: false
          Mutable: true

  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      ClientName: !Sub "${ProjectName}-client-${Environment}"
      UserPoolId: !Ref UserPool
      GenerateSecret: true
      ExplicitAuthFlows:
        - ADMIN_NO_SRP_AUTH
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
      RefreshTokenValidity: 30
      AccessTokenValidity: 60
      IdTokenValidity: 60

  IdentityPool:
    Type: AWS::Cognito::IdentityPool
    Properties:
      IdentityPoolName: !Sub "${ProjectName}-identity-${Environment}"
      AllowUnauthenticatedIdentities: false
      CognitoIdentityProviders:
        - ClientId: !Ref UserPoolClient
          ProviderName: !GetAtt UserPool.ProviderName
          ServerSideTokenCheck: false

  # IAM Roles for Cognito Identity Pool
  CognitoAuthenticatedRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${ProjectName}-authenticated-role-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Federated: cognito-identity.amazonaws.com
            Action: sts:AssumeRoleWithWebIdentity
            Condition:
              StringEquals:
                "cognito-identity.amazonaws.com:aud": !Ref IdentityPool
              "ForAnyValue:StringLike":
                "cognito-identity.amazonaws.com:amr": authenticated
      Policies:
        - PolicyName: CognitoAuthenticatedPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                Resource:
                  - !Sub "${MediaBucket.Arn}/*"
                  - !Sub "${AvatarsBucket.Arn}/*"
                  - !Sub "${TempBucket.Arn}/*"
              - Effect: Allow
                Action:
                  - s3:ListBucket
                Resource:
                  - !GetAtt MediaBucket.Arn
                  - !GetAtt AvatarsBucket.Arn
                  - !GetAtt TempBucket.Arn

  IdentityPoolRoleAttachment:
    Type: AWS::Cognito::IdentityPoolRoleAttachment
    Properties:
      IdentityPoolId: !Ref IdentityPool
      Roles:
        authenticated: !GetAtt CognitoAuthenticatedRole.Arn

  # Lambda Execution Role
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${ProjectName}-lambda-execution-role-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: CognitoAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - cognito-idp:*
                Resource: !GetAtt UserPool.Arn
        - PolicyName: S3Access
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !GetAtt MediaBucket.Arn
                  - !Sub "${MediaBucket.Arn}/*"
                  - !GetAtt AvatarsBucket.Arn
                  - !Sub "${AvatarsBucket.Arn}/*"
                  - !GetAtt TempBucket.Arn
                  - !Sub "${TempBucket.Arn}/*"

  # API Gateway
  ApiGateway:
    Type: AWS::ApiGateway::RestApi
    Properties:
      Name: !Sub "${ProjectName}-api-${Environment}"
      Description: GameFlex API Gateway
      EndpointConfiguration:
        Types:
          - REGIONAL

  # API Gateway Deployment
  ApiDeployment:
    Type: AWS::ApiGateway::Deployment
    DependsOn:
      - AuthResource
      - AuthSignupMethod
      - AuthSigninMethod
      - AuthRefreshMethod
      - AuthSignoutMethod
      - PostsResource
      - PostsGetMethod
      - PostsPostMethod
      - PostsByIdResource
      - PostsByIdGetMethod
      - MediaResource
      - MediaUploadResource
      - MediaUploadMethod
      - MediaByIdResource
      - MediaByIdGetMethod
    Properties:
      RestApiId: !Ref ApiGateway
      StageName: !Ref Environment

  # API Gateway Stage (for custom domain mapping)
  ApiStage:
    Type: AWS::ApiGateway::Stage
    Properties:
      RestApiId: !Ref ApiGateway
      DeploymentId: !Ref ApiDeployment
      StageName: api
      Description: API stage for custom domain mapping

  # Auth Resource
  AuthResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: auth

  # Auth Signup Resource
  AuthSignupResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref AuthResource
      PathPart: signup

  AuthSignupMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref AuthSignupResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${AuthLambda.Arn}/invocations"

  # Auth Signin Resource
  AuthSigninResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref AuthResource
      PathPart: signin

  AuthSigninMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref AuthSigninResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${AuthLambda.Arn}/invocations"

  # Auth Refresh Resource
  AuthRefreshResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref AuthResource
      PathPart: refresh

  AuthRefreshMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref AuthRefreshResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${AuthLambda.Arn}/invocations"

  # Auth Signout Resource
  AuthSignoutResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref AuthResource
      PathPart: signout

  AuthSignoutMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref AuthSignoutResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${AuthLambda.Arn}/invocations"

  # Posts Resources
  PostsResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: posts

  PostsGetMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref PostsResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${PostsLambda.Arn}/invocations"

  PostsPostMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref PostsResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${PostsLambda.Arn}/invocations"

  # Posts by ID Resource
  PostsByIdResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref PostsResource
      PathPart: "{id}"

  PostsByIdGetMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref PostsByIdResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${PostsLambda.Arn}/invocations"

  # Media Resources
  MediaResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: media

  MediaUploadResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref MediaResource
      PathPart: upload

  MediaUploadMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref MediaUploadResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${MediaLambda.Arn}/invocations"

  # Media by ID Resource
  MediaByIdResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref MediaResource
      PathPart: "{id}"

  MediaByIdGetMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref MediaByIdResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${MediaLambda.Arn}/invocations"

  # Users Resources
  UsersResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: users

  # Users Profile Resource
  UsersProfileResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref UsersResource
      PathPart: profile

  # Users Profile by ID Resource
  UsersProfileByIdResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref UsersProfileResource
      PathPart: "{userId}"

  UsersProfileByIdGetMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref UsersProfileByIdResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${UsersLambda.Arn}/invocations"

  UsersProfilePutMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref UsersProfileResource
      HttpMethod: PUT
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${UsersLambda.Arn}/invocations"

  # Users Posts Resource
  UsersPostsResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref UsersResource
      PathPart: posts

  # Users Posts by ID Resource
  UsersPostsByIdResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref UsersPostsResource
      PathPart: "{userId}"

  UsersPostsByIdGetMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref UsersPostsByIdResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${UsersLambda.Arn}/invocations"

  # Lambda Functions
  AuthLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${ProjectName}-auth-${Environment}"
      Runtime: nodejs18.x
      Handler: dist/handler.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Code:
        ZipFile: |
          def lambda_handler(event, context):
              return {
                  'statusCode': 200,
                  'body': '{"message": "Auth function placeholder"}'
              }
      Environment:
        Variables:
          COGNITO_USER_POOL_ID: !Ref UserPool
          COGNITO_USER_POOL_CLIENT_ID: !Ref UserPoolClient
          DYNAMODB_ENDPOINT_URL:
            !If [
              IsDevelopment,
              "http://localhost:4566",
              !Sub "https://dynamodb.${AWS::Region}.amazonaws.com",
            ]
          DYNAMODB_REGION: !Ref AWS::Region
          DYNAMODB_TABLE_USERS: !Ref UsersTable
          DYNAMODB_TABLE_USER_PROFILES: !Ref UserProfilesTable
          DYNAMODB_TABLE_CHANNELS: !Ref ChannelsTable
          DYNAMODB_TABLE_CHANNEL_MEMBERS: !Ref ChannelMembersTable
          DYNAMODB_TABLE_MEDIA: !Ref MediaTable
          DYNAMODB_TABLE_POSTS: !Ref PostsTable
          DYNAMODB_TABLE_COMMENTS: !Ref CommentsTable
          DYNAMODB_TABLE_LIKES: !Ref LikesTable
          DYNAMODB_TABLE_FOLLOWS: !Ref FollowsTable
          DYNAMODB_TABLE_NOTIFICATIONS: !Ref NotificationsTable
          AWS_ENDPOINT_URL: !If [IsDevelopment, "http://localhost:45660", ""]

  PostsLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${ProjectName}-posts-${Environment}"
      Runtime: nodejs18.x
      Handler: dist/handler.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Code:
        ZipFile: |
          def lambda_handler(event, context):
              return {
                  'statusCode': 200,
                  'body': '{"message": "Posts function placeholder"}'
              }
      Environment:
        Variables:
          COGNITO_USER_POOL_ID: !Ref UserPool
          DYNAMODB_ENDPOINT_URL:
            !If [
              IsDevelopment,
              "http://localhost:4566",
              !Sub "https://dynamodb.${AWS::Region}.amazonaws.com",
            ]
          DYNAMODB_REGION: !Ref AWS::Region
          DYNAMODB_TABLE_USERS: !Ref UsersTable
          DYNAMODB_TABLE_USER_PROFILES: !Ref UserProfilesTable
          DYNAMODB_TABLE_CHANNELS: !Ref ChannelsTable
          DYNAMODB_TABLE_CHANNEL_MEMBERS: !Ref ChannelMembersTable
          DYNAMODB_TABLE_MEDIA: !Ref MediaTable
          DYNAMODB_TABLE_POSTS: !Ref PostsTable
          DYNAMODB_TABLE_COMMENTS: !Ref CommentsTable
          DYNAMODB_TABLE_LIKES: !Ref LikesTable
          DYNAMODB_TABLE_FOLLOWS: !Ref FollowsTable
          DYNAMODB_TABLE_NOTIFICATIONS: !Ref NotificationsTable
          S3_BUCKET_MEDIA: !Ref MediaBucket
          AWS_ENDPOINT_URL: !If [IsDevelopment, "http://localhost:45660", ""]

  MediaLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${ProjectName}-media-${Environment}"
      Runtime: nodejs18.x
      Handler: dist/handler.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 60
      MemorySize: 512
      Code:
        ZipFile: |
          def lambda_handler(event, context):
              return {
                  'statusCode': 200,
                  'body': '{"message": "Media function placeholder"}'
              }
      Environment:
        Variables:
          COGNITO_USER_POOL_ID: !Ref UserPool
          DYNAMODB_ENDPOINT_URL:
            !If [
              IsDevelopment,
              "http://localhost:4566",
              !Sub "https://dynamodb.${AWS::Region}.amazonaws.com",
            ]
          DYNAMODB_REGION: !Ref AWS::Region
          DYNAMODB_TABLE_USERS: !Ref UsersTable
          DYNAMODB_TABLE_USER_PROFILES: !Ref UserProfilesTable
          DYNAMODB_TABLE_CHANNELS: !Ref ChannelsTable
          DYNAMODB_TABLE_CHANNEL_MEMBERS: !Ref ChannelMembersTable
          DYNAMODB_TABLE_MEDIA: !Ref MediaTable
          DYNAMODB_TABLE_POSTS: !Ref PostsTable
          DYNAMODB_TABLE_COMMENTS: !Ref CommentsTable
          DYNAMODB_TABLE_LIKES: !Ref LikesTable
          DYNAMODB_TABLE_FOLLOWS: !Ref FollowsTable
          DYNAMODB_TABLE_NOTIFICATIONS: !Ref NotificationsTable
          S3_BUCKET_MEDIA: !Ref MediaBucket
          S3_BUCKET_AVATARS: !Ref AvatarsBucket
          S3_BUCKET_TEMP: !Ref TempBucket
          MAX_FILE_SIZE: "52428800"
          AWS_ENDPOINT_URL: !If [IsDevelopment, "http://localhost:45660", ""]

  UsersLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub "${ProjectName}-users-${Environment}"
      Runtime: nodejs18.x
      Handler: dist/handler.handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 60
      MemorySize: 512
      Code:
        ZipFile: |
          def lambda_handler(event, context):
              return {
                  'statusCode': 200,
                  'body': '{"message": "Users function placeholder"}'
              }
      Environment:
        Variables:
          COGNITO_USER_POOL_ID: !Ref UserPool
          COGNITO_USER_POOL_CLIENT_ID: !Ref UserPoolClient
          DYNAMODB_ENDPOINT_URL:
            !If [
              IsDevelopment,
              "http://localhost:4566",
              !Sub "https://dynamodb.${AWS::Region}.amazonaws.com",
            ]
          DYNAMODB_REGION: !Ref AWS::Region
          DYNAMODB_TABLE_USERS: !Ref UsersTable
          DYNAMODB_TABLE_USER_PROFILES: !Ref UserProfilesTable
          DYNAMODB_TABLE_POSTS: !Ref PostsTable
          DYNAMODB_TABLE_LIKES: !Ref LikesTable
          DYNAMODB_TABLE_FOLLOWS: !Ref FollowsTable
          AWS_ENDPOINT_URL: !If [IsDevelopment, "http://localhost:45660", ""]

  # Lambda Permissions
  AuthLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref AuthLambda
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*"

  PostsLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref PostsLambda
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*"

  MediaLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref MediaLambda
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*"

  UsersLambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref UsersLambda
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*"

Outputs:
  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref UserPool
    Export:
      Name: !Sub "${ProjectName}-user-pool-id-${Environment}"

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref UserPoolClient
    Export:
      Name: !Sub "${ProjectName}-user-pool-client-id-${Environment}"

  IdentityPoolId:
    Description: Cognito Identity Pool ID
    Value: !Ref IdentityPool
    Export:
      Name: !Sub "${ProjectName}-identity-pool-id-${Environment}"

  ApiGatewayUrl:
    Description: API Gateway URL
    Value: !If
      - IsDevelopment
      - !Sub "http://${ApiDomainName}:45660/api"
      - !Sub "https://${ApiDomainName}/api"
    Export:
      Name: !Sub "${ProjectName}-api-url-${Environment}"

  MediaBucketName:
    Description: S3 Media Bucket Name
    Value: !Ref MediaBucket
    Export:
      Name: !Sub "${ProjectName}-media-bucket-${Environment}"

  AvatarsBucketName:
    Description: S3 Avatars Bucket Name
    Value: !Ref AvatarsBucket
    Export:
      Name: !Sub "${ProjectName}-avatars-bucket-${Environment}"

  AuthLambdaLogGroupName:
    Description: Auth Lambda CloudWatch Log Group Name (Auto-created)
    Value: !Sub "/aws/lambda/${ProjectName}-auth-${Environment}"

  PostsLambdaLogGroupName:
    Description: Posts Lambda CloudWatch Log Group Name (Auto-created)
    Value: !Sub "/aws/lambda/${ProjectName}-posts-${Environment}"

  MediaLambdaLogGroupName:
    Description: Media Lambda CloudWatch Log Group Name (Auto-created)
    Value: !Sub "/aws/lambda/${ProjectName}-media-${Environment}"

  UsersLambdaLogGroupName:
    Description: Users Lambda CloudWatch Log Group Name (Auto-created)
    Value: !Sub "/aws/lambda/${ProjectName}-users-${Environment}"
