# Check API Gateway Configuration Script
# This script helps diagnose API Gateway setup and provides static URL information

param(
    [string]$EndpointUrl = "http://localhost:45660"
)

# Set AWS credentials for LocalStack
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = "us-east-1"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Header {
    param([string]$Message)
    Write-Host "`n=== $Message ===" -ForegroundColor Blue
}

Write-Header "API Gateway Configuration Check"

# Check if LocalStack is running
Write-Status "Checking LocalStack health..."
try {
    $health = Invoke-RestMethod -Uri "$EndpointUrl/_localstack/health" -Method GET
    Write-Status "LocalStack is running"
} catch {
    Write-Host "[ERROR] LocalStack is not running or not accessible at $EndpointUrl" -ForegroundColor Red
    exit 1
}

# List all API Gateways
Write-Header "Available API Gateways"
try {
    $apis = aws --endpoint-url=$EndpointUrl apigateway get-rest-apis --output json | ConvertFrom-Json
    
    if ($apis.items -and $apis.items.Count -gt 0) {
        foreach ($api in $apis.items) {
            Write-Host "  Name: $($api.name)" -ForegroundColor Cyan
            Write-Host "  ID: $($api.id)" -ForegroundColor Yellow
            Write-Host "  Created: $($api.createdDate)" -ForegroundColor Gray
            
            # Show the current dynamic URL
            $dynamicUrl = "$EndpointUrl/restapis/$($api.id)/development/_user_request_"
            Write-Host "  Current Dynamic URL: $dynamicUrl" -ForegroundColor Magenta
            Write-Host ""
        }
    } else {
        Write-Host "  No API Gateways found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "[ERROR] Failed to list API Gateways: $_" -ForegroundColor Red
}

# Check CloudFormation stack outputs
Write-Header "CloudFormation Stack Outputs"
try {
    $outputs = aws --endpoint-url=$EndpointUrl cloudformation describe-stacks --stack-name gameflex-infrastructure-development --query "Stacks[0].Outputs" --output json 2>$null | ConvertFrom-Json
    
    if ($outputs) {
        foreach ($output in $outputs) {
            Write-Host "  $($output.OutputKey): $($output.OutputValue)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "  No stack outputs found or stack doesn't exist" -ForegroundColor Yellow
    }
} catch {
    Write-Host "[ERROR] Failed to get CloudFormation outputs: $_" -ForegroundColor Red
}

# Show recommended static URL approach
Write-Header "Recommended Static URL Configuration"
Write-Host "Instead of using dynamic API Gateway IDs, you can use:" -ForegroundColor Green
Write-Host "  Static URL: $EndpointUrl/api" -ForegroundColor Cyan
Write-Host ""
Write-Host "This requires:" -ForegroundColor Yellow
Write-Host "  1. Updated CloudFormation template (✓ Done)" -ForegroundColor Green
Write-Host "  2. Updated Flutter ApiService (✓ Done)" -ForegroundColor Green
Write-Host "  3. Updated test scripts (✓ Done)" -ForegroundColor Green
Write-Host "  4. API Gateway custom domain or proxy setup" -ForegroundColor Orange
Write-Host ""

# Test the static URL approach
Write-Header "Testing Static URL Approach"
Write-Status "Testing if /api endpoint is accessible..."

try {
    # Try to access the static API endpoint
    $response = Invoke-WebRequest -Uri "$EndpointUrl/api/health" -Method GET -ErrorAction SilentlyContinue
    Write-Host "  Static URL test: SUCCESS (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "  Static URL test: FAILED - $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  This is expected if custom domain/proxy is not set up yet" -ForegroundColor Yellow
}

Write-Header "Summary"
Write-Host "Current setup uses dynamic API Gateway IDs which change on each deployment." -ForegroundColor Yellow
Write-Host "The code has been updated to support static URLs, but LocalStack needs configuration" -ForegroundColor Yellow
Write-Host "to map /api/* to the actual API Gateway endpoints." -ForegroundColor Yellow
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "  1. Test the current dynamic URL approach to ensure it works" -ForegroundColor White
Write-Host "  2. Consider implementing a simple proxy or custom domain setup" -ForegroundColor White
Write-Host "  3. Or revert to the working dynamic ID approach with better caching" -ForegroundColor White
