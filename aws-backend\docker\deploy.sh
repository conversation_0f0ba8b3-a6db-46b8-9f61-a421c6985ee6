#!/bin/bash
set -e

# CloudFormation Deployment Script
# This script deploys CloudFormation templates and updates Lambda functions
#
# WARNING: This script uses gameflex-simple-infrastructure.yaml which does NOT include API Gateway
# If you need API Gateway, use start.ps1 instead which deploys gameflex-infrastructure.yaml
#
# This script is intended for containerized deployments that don't need API Gateway

echo "🚀 CloudFormation Deployer Starting..."

# Set default values
ENVIRONMENT=${ENVIRONMENT:-development}
PROJECT_NAME=${PROJECT_NAME:-gameflex}
AWS_ENDPOINT_URL=${AWS_ENDPOINT_URL:-http://host.docker.internal:45660}

echo "📋 Configuration:"
echo "  Environment: $ENVIRONMENT"
echo "  Project: $PROJECT_NAME"
echo "  AWS Endpoint: $AWS_ENDPOINT_URL"

# Test AWS CLI connection
echo "🔗 Testing AWS CLI connection..."
aws --endpoint-url="$AWS_ENDPOINT_URL" sts get-caller-identity > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ AWS CLI connection successful"
else
    echo "❌ AWS CLI connection failed"
    exit 1
fi

# Deploy CloudFormation stack
# Use a different stack name to avoid conflicts with start.ps1 which uses gameflex-infrastructure-development
STACK_NAME="$PROJECT_NAME-simple-infrastructure-$ENVIRONMENT"
TEMPLATE_FILE="/workspace/cloudformation/gameflex-simple-infrastructure.yaml"
PARAMETERS_FILE="/workspace/cloudformation/parameters/$ENVIRONMENT.json"

echo "📦 Deploying CloudFormation stack: $STACK_NAME"
echo "📋 Using simple infrastructure template (no API Gateway)"

if [ ! -f "$TEMPLATE_FILE" ]; then
    echo "❌ Template file not found: $TEMPLATE_FILE"
    exit 1
fi

if [ ! -f "$PARAMETERS_FILE" ]; then
    echo "❌ Parameters file not found: $PARAMETERS_FILE"
    exit 1
fi

# Check if stack exists
aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation describe-stacks --stack-name "$STACK_NAME" > /dev/null 2>&1
STACK_EXISTS=$?

if [ $STACK_EXISTS -eq 0 ]; then
    echo "🔄 Stack exists, updating..."
    aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation update-stack \
        --stack-name "$STACK_NAME" \
        --template-body "file://$TEMPLATE_FILE" \
        --capabilities CAPABILITY_NAMED_IAM \
        --parameters "file://$PARAMETERS_FILE" \
        --tags Key=Environment,Value="$ENVIRONMENT" Key=Project,Value="$PROJECT_NAME" \
        > /dev/null 2>&1 || echo "⚠️  Stack update may have failed or no changes detected"
else
    echo "🆕 Creating new stack..."
    aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation create-stack \
        --stack-name "$STACK_NAME" \
        --template-body "file://$TEMPLATE_FILE" \
        --capabilities CAPABILITY_NAMED_IAM \
        --parameters "file://$PARAMETERS_FILE" \
        --tags Key=Environment,Value="$ENVIRONMENT" Key=Project,Value="$PROJECT_NAME" \
        > /dev/null 2>&1
fi

echo "⏳ Waiting for stack deployment to complete..."
aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation wait stack-update-complete --stack-name "$STACK_NAME" 2>/dev/null || \
aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation wait stack-create-complete --stack-name "$STACK_NAME" 2>/dev/null || \
echo "⚠️  Stack deployment completed with warnings"

# Update Lambda functions if packages exist
PACKAGES_DIR="/workspace/packages"
if [ -d "$PACKAGES_DIR" ]; then
    echo "🔄 Updating Lambda functions..."
    
    for package in "$PACKAGES_DIR"/*.zip; do
        if [ -f "$package" ]; then
            FUNCTION_NAME=$(basename "$package" .zip)
            FULL_FUNCTION_NAME="$PROJECT_NAME-$FUNCTION_NAME-$ENVIRONMENT"
            
            echo "📦 Updating Lambda function: $FULL_FUNCTION_NAME"
            aws --endpoint-url="$AWS_ENDPOINT_URL" lambda update-function-code \
                --function-name "$FULL_FUNCTION_NAME" \
                --zip-file "fileb://$package" \
                > /dev/null 2>&1 && echo "✅ Updated $FULL_FUNCTION_NAME" || echo "⚠️  Failed to update $FULL_FUNCTION_NAME"
        fi
    done
else
    echo "⚠️  No packages directory found, skipping Lambda updates"
fi

echo "🎉 Deployment completed successfully!"

# Show stack outputs
echo "📋 Stack Outputs:"
aws --endpoint-url="$AWS_ENDPOINT_URL" cloudformation describe-stacks \
    --stack-name "$STACK_NAME" \
    --query 'Stacks[0].Outputs[*].[OutputKey,OutputValue]' \
    --output table 2>/dev/null || echo "⚠️  Could not retrieve stack outputs"
